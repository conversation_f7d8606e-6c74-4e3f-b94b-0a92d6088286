# Database Configuration
MONGODB_URI=mongodb://localhost:27017/criminal_records
# For MongoDB Atlas: mongodb+srv://username:<EMAIL>/criminal_records

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRES_IN=7d

# Server Configuration
PORT=5000
NODE_ENV=development

# Admin Credentials (for initial setup)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# File Upload Configuration
UPLOAD_PATH=uploads/
MAX_FILE_SIZE=5242880

# CORS Configuration
FRONTEND_URL=http://localhost:5173

# Optional: Map API Keys (for future features)
# GOOGLE_MAPS_API_KEY=your_google_maps_api_key
# MAPBOX_ACCESS_TOKEN=your_mapbox_access_token
