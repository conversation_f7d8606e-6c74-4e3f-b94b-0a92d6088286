import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { crimeRecordAPI, criminalAPI } from '../utils/api';

interface Criminal {
  _id: string;
  fullName: string;
  alias?: string;
  nationalId?: string;
}

interface CrimeRecordForm {
  criminalId: string;
  type: string;
  subType: string;
  description: string;
  date: string;
  time: string;
  location: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
  };
  outcome: string;
  severity: string;
  officer: {
    name: string;
    badgeNumber: string;
    department: string;
  };
  arrestingOfficer: {
    name: string;
    badgeNumber: string;
    department: string;
  };
  courtDate: string;
  sentence: string;
  fine: string;
  notes: string;
}

const AddCrimeRecord = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [criminals, setCriminals] = useState<Criminal[]>([]);
  const [selectedCriminal, setSelectedCriminal] = useState<Criminal | null>(null);
  const [criminalSearch, setCriminalSearch] = useState('');

  const [formData, setFormData] = useState<CrimeRecordForm>({
    criminalId: searchParams.get('criminalId') || '',
    type: '',
    subType: '',
    description: '',
    date: new Date().toISOString().split('T')[0],
    time: '',
    location: {
      address: '',
      city: '',
      state: '',
      zipCode: ''
    },
    outcome: 'Under Investigation',
    severity: 'Misdemeanor',
    officer: {
      name: '',
      badgeNumber: '',
      department: ''
    },
    arrestingOfficer: {
      name: '',
      badgeNumber: '',
      department: ''
    },
    courtDate: '',
    sentence: '',
    fine: '',
    notes: ''
  });

  const crimeTypes = [
    'Theft', 'Burglary', 'Robbery', 'Assault', 'Battery', 'Domestic Violence',
    'Drug Possession', 'Drug Trafficking', 'Fraud', 'Identity Theft', 'Cybercrime',
    'Vandalism', 'Trespassing', 'Weapons Violation', 'DUI/DWI', 'Traffic Violation',
    'Homicide', 'Sexual Assault', 'Kidnapping', 'Arson', 'Money Laundering',
    'Organized Crime', 'Public Disorder', 'Other'
  ];

  const outcomes = [
    'Under Investigation', 'Arrested', 'Released', 'Charged', 'Convicted',
    'Acquitted', 'Case Closed', 'Dismissed', 'Plea Bargain', 'Pending Trial',
    'Warrant Issued', 'Suspect at Large'
  ];

  useEffect(() => {
    // If criminalId is provided in URL, fetch that criminal
    if (formData.criminalId) {
      fetchCriminalById(formData.criminalId);
    }
  }, [formData.criminalId]);

  const fetchCriminalById = async (id: string) => {
    try {
      const response = await criminalAPI.getById(id);
      setSelectedCriminal(response.data.data.criminal);
    } catch (err) {
      console.error('Failed to fetch criminal:', err);
    }
  };

  const searchCriminals = async (searchTerm: string) => {
    if (!searchTerm.trim()) {
      setCriminals([]);
      return;
    }

    try {
      const response = await criminalAPI.getAll({ search: searchTerm, limit: 10 });
      setCriminals(response.data.data);
    } catch (err) {
      console.error('Failed to search criminals:', err);
    }
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchCriminals(criminalSearch);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [criminalSearch]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof CrimeRecordForm] as any,
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const selectCriminal = (criminal: Criminal) => {
    setSelectedCriminal(criminal);
    setFormData(prev => ({ ...prev, criminalId: criminal._id }));
    setCriminalSearch(criminal.fullName);
    setCriminals([]);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Clean up the data before sending
      const cleanedData = {
        ...formData,
        subType: formData.subType || undefined,
        time: formData.time || undefined,
        location: {
          ...formData.location,
          state: formData.location.state || undefined,
          zipCode: formData.location.zipCode || undefined
        },
        officer: formData.officer.name ? formData.officer : undefined,
        arrestingOfficer: formData.arrestingOfficer.name ? formData.arrestingOfficer : undefined,
        courtDate: formData.courtDate || undefined,
        sentence: formData.sentence || undefined,
        fine: formData.fine ? Number(formData.fine) : undefined,
        notes: formData.notes || undefined
      };

      await crimeRecordAPI.create(cleanedData);
      navigate('/crime-records');
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to create crime record');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Add Crime Record</h1>
        <p className="text-gray-600">Log a new crime incident</p>
      </div>

      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-700">{error}</div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Criminal Selection */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium">Criminal Information</h3>
          </div>
          <div className="card-content">
            {selectedCriminal ? (
              <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
                <div>
                  <h4 className="font-medium text-green-900">{selectedCriminal.fullName}</h4>
                  {selectedCriminal.alias && (
                    <p className="text-sm text-green-700">Alias: {selectedCriminal.alias}</p>
                  )}
                  {selectedCriminal.nationalId && (
                    <p className="text-sm text-green-700">ID: {selectedCriminal.nationalId}</p>
                  )}
                </div>
                <button
                  type="button"
                  onClick={() => {
                    setSelectedCriminal(null);
                    setFormData(prev => ({ ...prev, criminalId: '' }));
                    setCriminalSearch('');
                  }}
                  className="text-green-600 hover:text-green-800 text-sm"
                >
                  Change
                </button>
              </div>
            ) : (
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Search Criminal *
                </label>
                <input
                  type="text"
                  placeholder="Search by name, alias, or national ID..."
                  className="input"
                  value={criminalSearch}
                  onChange={(e) => setCriminalSearch(e.target.value)}
                  required
                />
                {criminals.length > 0 && (
                  <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                    {criminals.map((criminal) => (
                      <button
                        key={criminal._id}
                        type="button"
                        onClick={() => selectCriminal(criminal)}
                        className="w-full text-left px-4 py-2 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                      >
                        <div className="font-medium text-gray-900">{criminal.fullName}</div>
                        {criminal.alias && (
                          <div className="text-sm text-gray-500">Alias: {criminal.alias}</div>
                        )}
                        {criminal.nationalId && (
                          <div className="text-sm text-gray-500">ID: {criminal.nationalId}</div>
                        )}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Crime Details */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium">Crime Details</h3>
          </div>
          <div className="card-content">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Crime Type *
                </label>
                <select
                  name="type"
                  required
                  className="input"
                  value={formData.type}
                  onChange={handleInputChange}
                >
                  <option value="">Select crime type</option>
                  {crimeTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Crime Subtype
                </label>
                <input
                  type="text"
                  name="subType"
                  className="input"
                  value={formData.subType}
                  onChange={handleInputChange}
                  placeholder="Specific type or category"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date *
                </label>
                <input
                  type="date"
                  name="date"
                  required
                  className="input"
                  value={formData.date}
                  onChange={handleInputChange}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Time
                </label>
                <input
                  type="time"
                  name="time"
                  className="input"
                  value={formData.time}
                  onChange={handleInputChange}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Outcome *
                </label>
                <select
                  name="outcome"
                  required
                  className="input"
                  value={formData.outcome}
                  onChange={handleInputChange}
                >
                  {outcomes.map(outcome => (
                    <option key={outcome} value={outcome}>{outcome}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Severity *
                </label>
                <select
                  name="severity"
                  required
                  className="input"
                  value={formData.severity}
                  onChange={handleInputChange}
                >
                  <option value="Infraction">Infraction</option>
                  <option value="Misdemeanor">Misdemeanor</option>
                  <option value="Felony">Felony</option>
                </select>
              </div>
            </div>

            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description *
              </label>
              <textarea
                name="description"
                required
                rows={4}
                className="input"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Detailed description of the crime incident..."
              />
            </div>
          </div>
        </div>

        {/* Location Information */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium">Location Information</h3>
          </div>
          <div className="card-content">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Address *
                </label>
                <input
                  type="text"
                  name="location.address"
                  required
                  className="input"
                  value={formData.location.address}
                  onChange={handleInputChange}
                  placeholder="Street address where crime occurred"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  City *
                </label>
                <input
                  type="text"
                  name="location.city"
                  required
                  className="input"
                  value={formData.location.city}
                  onChange={handleInputChange}
                  placeholder="City"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  State
                </label>
                <input
                  type="text"
                  name="location.state"
                  className="input"
                  value={formData.location.state}
                  onChange={handleInputChange}
                  placeholder="State"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  ZIP Code
                </label>
                <input
                  type="text"
                  name="location.zipCode"
                  className="input"
                  value={formData.location.zipCode}
                  onChange={handleInputChange}
                  placeholder="ZIP Code"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Officer Information */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium">Officer Information</h3>
          </div>
          <div className="card-content">
            <div className="space-y-6">
              {/* Reporting Officer */}
              <div>
                <h4 className="font-medium text-gray-900 mb-4">Reporting Officer</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Officer Name
                    </label>
                    <input
                      type="text"
                      name="officer.name"
                      className="input"
                      value={formData.officer.name}
                      onChange={handleInputChange}
                      placeholder="Officer name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Badge Number
                    </label>
                    <input
                      type="text"
                      name="officer.badgeNumber"
                      className="input"
                      value={formData.officer.badgeNumber}
                      onChange={handleInputChange}
                      placeholder="Badge number"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Department
                    </label>
                    <input
                      type="text"
                      name="officer.department"
                      className="input"
                      value={formData.officer.department}
                      onChange={handleInputChange}
                      placeholder="Department"
                    />
                  </div>
                </div>
              </div>

              {/* Arresting Officer */}
              <div>
                <h4 className="font-medium text-gray-900 mb-4">Arresting Officer (if different)</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Officer Name
                    </label>
                    <input
                      type="text"
                      name="arrestingOfficer.name"
                      className="input"
                      value={formData.arrestingOfficer.name}
                      onChange={handleInputChange}
                      placeholder="Arresting officer name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Badge Number
                    </label>
                    <input
                      type="text"
                      name="arrestingOfficer.badgeNumber"
                      className="input"
                      value={formData.arrestingOfficer.badgeNumber}
                      onChange={handleInputChange}
                      placeholder="Badge number"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Department
                    </label>
                    <input
                      type="text"
                      name="arrestingOfficer.department"
                      className="input"
                      value={formData.arrestingOfficer.department}
                      onChange={handleInputChange}
                      placeholder="Department"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium">Additional Information</h3>
          </div>
          <div className="card-content">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Court Date
                </label>
                <input
                  type="date"
                  name="courtDate"
                  className="input"
                  value={formData.courtDate}
                  onChange={handleInputChange}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Fine Amount
                </label>
                <input
                  type="number"
                  name="fine"
                  className="input"
                  value={formData.fine}
                  onChange={handleInputChange}
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Sentence
                </label>
                <input
                  type="text"
                  name="sentence"
                  className="input"
                  value={formData.sentence}
                  onChange={handleInputChange}
                  placeholder="Sentence details (if applicable)"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Additional Notes
                </label>
                <textarea
                  name="notes"
                  rows={4}
                  className="input"
                  value={formData.notes}
                  onChange={handleInputChange}
                  placeholder="Any additional notes or observations..."
                />
              </div>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => navigate('/crime-records')}
            className="btn-outline"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn-primary"
            disabled={isLoading || !formData.criminalId}
          >
            {isLoading ? (
              <div className="flex items-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Creating...
              </div>
            ) : (
              'Create Crime Record'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AddCrimeRecord;
