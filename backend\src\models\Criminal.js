const mongoose = require('mongoose');

const criminalSchema = new mongoose.Schema({
  // Personal Information
  fullName: {
    type: String,
    required: [true, 'Full name is required'],
    trim: true,
    maxlength: [100, 'Full name cannot exceed 100 characters']
  },
  alias: {
    type: String,
    trim: true,
    maxlength: [50, 'Alias cannot exceed 50 characters']
  },
  nationalId: {
    type: String,
    unique: true,
    sparse: true, // Allows multiple null values
    trim: true,
    maxlength: [20, 'National ID cannot exceed 20 characters']
  },
  gender: {
    type: String,
    enum: ['Male', 'Female', 'Other', 'Unknown'],
    required: [true, 'Gender is required']
  },
  birthDate: {
    type: Date,
    validate: {
      validator: function(value) {
        return value <= new Date();
      },
      message: 'Birth date cannot be in the future'
    }
  },
  
  // Physical Characteristics
  photoUrl: {
    type: String,
    trim: true
  },
  distinctiveMarks: {
    type: String,
    maxlength: [500, 'Distinctive marks description cannot exceed 500 characters']
  },
  fingerprintId: {
    type: String,
    unique: true,
    sparse: true,
    trim: true
  },
  
  // Location and Association Data
  knownAddresses: [{
    address: {
      type: String,
      required: true,
      trim: true
    },
    city: {
      type: String,
      required: true,
      trim: true
    },
    state: {
      type: String,
      trim: true
    },
    zipCode: {
      type: String,
      trim: true
    },
    isActive: {
      type: Boolean,
      default: true
    },
    dateAdded: {
      type: Date,
      default: Date.now
    }
  }],
  
  knownVehicles: [{
    make: {
      type: String,
      required: true,
      trim: true
    },
    model: {
      type: String,
      required: true,
      trim: true
    },
    year: {
      type: Number,
      min: 1900,
      max: new Date().getFullYear() + 1
    },
    color: {
      type: String,
      trim: true
    },
    licensePlate: {
      type: String,
      trim: true,
      uppercase: true
    },
    isActive: {
      type: Boolean,
      default: true
    },
    dateAdded: {
      type: Date,
      default: Date.now
    }
  }],
  
  gangAffiliation: {
    type: String,
    trim: true,
    maxlength: [100, 'Gang affiliation cannot exceed 100 characters']
  },
  
  // Additional Information
  notes: {
    type: String,
    maxlength: [1000, 'Notes cannot exceed 1000 characters']
  },
  
  // System Fields
  isActive: {
    type: Boolean,
    default: true
  },
  riskLevel: {
    type: String,
    enum: ['Low', 'Medium', 'High', 'Critical'],
    default: 'Low'
  },
  createdBy: {
    type: String,
    required: true
  },
  lastModifiedBy: {
    type: String,
    required: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for age calculation
criminalSchema.virtual('age').get(function() {
  if (!this.birthDate) return null;
  const today = new Date();
  const birthDate = new Date(this.birthDate);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
});

// Virtual for crime count
criminalSchema.virtual('crimeCount', {
  ref: 'CrimeRecord',
  localField: '_id',
  foreignField: 'criminalId',
  count: true
});

// Indexes for better query performance
criminalSchema.index({ fullName: 'text', alias: 'text' });
criminalSchema.index({ nationalId: 1 });
criminalSchema.index({ 'knownAddresses.city': 1 });
criminalSchema.index({ gangAffiliation: 1 });
criminalSchema.index({ riskLevel: 1 });
criminalSchema.index({ createdAt: -1 });

// Pre-save middleware
criminalSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.lastModifiedBy = this.lastModifiedBy || 'system';
  }
  next();
});

module.exports = mongoose.model('Criminal', criminalSchema);
