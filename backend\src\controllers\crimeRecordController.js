const { CrimeRecord, Criminal } = require('../models');

// @desc    Get all crime records with filtering
// @route   GET /api/crime-records
// @access  Private
const getCrimeRecords = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      criminalId,
      type,
      city,
      outcome,
      severity,
      dateFrom,
      dateTo,
      sortBy = 'date',
      sortOrder = 'desc'
    } = req.query;

    // Build query object
    const query = { isActive: true };

    // Filters
    if (criminalId) query.criminalId = criminalId;
    if (type && type !== 'all') query.type = type;
    if (city) query['location.city'] = { $regex: city, $options: 'i' };
    if (outcome && outcome !== 'all') query.outcome = outcome;
    if (severity && severity !== 'all') query.severity = severity;

    // Date range filter
    if (dateFrom || dateTo) {
      query.date = {};
      if (dateFrom) query.date.$gte = new Date(dateFrom);
      if (dateTo) query.date.$lte = new Date(dateTo);
    }

    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Sort options
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with population
    const crimeRecords = await CrimeRecord.find(query)
      .populate('criminalId', 'fullName alias nationalId photoUrl')
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get total count for pagination
    const total = await CrimeRecord.countDocuments(query);

    res.status(200).json({
      success: true,
      count: crimeRecords.length,
      total,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      },
      data: crimeRecords
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single crime record by ID
// @route   GET /api/crime-records/:id
// @access  Private
const getCrimeRecord = async (req, res, next) => {
  try {
    const crimeRecord = await CrimeRecord.findById(req.params.id)
      .populate('criminalId');

    if (!crimeRecord || !crimeRecord.isActive) {
      return res.status(404).json({
        success: false,
        error: 'Crime record not found'
      });
    }

    res.status(200).json({
      success: true,
      data: crimeRecord
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create new crime record
// @route   POST /api/crime-records
// @access  Private
const createCrimeRecord = async (req, res, next) => {
  try {
    // Verify criminal exists
    const criminal = await Criminal.findById(req.body.criminalId);
    if (!criminal || !criminal.isActive) {
      return res.status(404).json({
        success: false,
        error: 'Criminal not found'
      });
    }

    // Add admin info to the crime record data
    req.body.createdBy = req.admin.fullName;
    req.body.lastModifiedBy = req.admin.fullName;

    // Set default officer if not provided
    if (!req.body.officer) {
      req.body.officer = {
        name: req.admin.fullName,
        badgeNumber: req.admin.badgeNumber,
        department: req.admin.department
      };
    }

    const crimeRecord = await CrimeRecord.create(req.body);

    // Populate the criminal data before returning
    await crimeRecord.populate('criminalId', 'fullName alias nationalId');

    res.status(201).json({
      success: true,
      data: crimeRecord
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update crime record
// @route   PUT /api/crime-records/:id
// @access  Private
const updateCrimeRecord = async (req, res, next) => {
  try {
    // Add admin info
    req.body.lastModifiedBy = req.admin.fullName;

    const crimeRecord = await CrimeRecord.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true
      }
    ).populate('criminalId', 'fullName alias nationalId');

    if (!crimeRecord) {
      return res.status(404).json({
        success: false,
        error: 'Crime record not found'
      });
    }

    res.status(200).json({
      success: true,
      data: crimeRecord
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete crime record (soft delete)
// @route   DELETE /api/crime-records/:id
// @access  Private
const deleteCrimeRecord = async (req, res, next) => {
  try {
    const crimeRecord = await CrimeRecord.findByIdAndUpdate(
      req.params.id,
      { 
        isActive: false,
        lastModifiedBy: req.admin.fullName
      },
      { new: true }
    );

    if (!crimeRecord) {
      return res.status(404).json({
        success: false,
        error: 'Crime record not found'
      });
    }

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get crime records by criminal ID
// @route   GET /api/crime-records/criminal/:criminalId
// @access  Private
const getCrimeRecordsByCriminal = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, sortBy = 'date', sortOrder = 'desc' } = req.query;

    // Verify criminal exists
    const criminal = await Criminal.findById(req.params.criminalId);
    if (!criminal || !criminal.isActive) {
      return res.status(404).json({
        success: false,
        error: 'Criminal not found'
      });
    }

    const query = { 
      criminalId: req.params.criminalId,
      isActive: true 
    };

    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Sort options
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const crimeRecords = await CrimeRecord.find(query)
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    const total = await CrimeRecord.countDocuments(query);

    res.status(200).json({
      success: true,
      count: crimeRecords.length,
      total,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      },
      data: crimeRecords
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get crime statistics by type
// @route   GET /api/crime-records/stats/by-type
// @access  Private
const getCrimeStatsByType = async (req, res, next) => {
  try {
    const stats = await CrimeRecord.aggregate([
      { $match: { isActive: true } },
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 },
          latestDate: { $max: '$date' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get crime statistics by location
// @route   GET /api/crime-records/stats/by-location
// @access  Private
const getCrimeStatsByLocation = async (req, res, next) => {
  try {
    const stats = await CrimeRecord.aggregate([
      { $match: { isActive: true } },
      {
        $group: {
          _id: '$location.city',
          count: { $sum: 1 },
          types: { $addToSet: '$type' },
          latestDate: { $max: '$date' }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 20 } // Top 20 cities
    ]);

    res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getCrimeRecords,
  getCrimeRecord,
  createCrimeRecord,
  updateCrimeRecord,
  deleteCrimeRecord,
  getCrimeRecordsByCriminal,
  getCrimeStatsByType,
  getCrimeStatsByLocation
};
