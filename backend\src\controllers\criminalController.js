const { Criminal, CrimeRecord } = require('../models');

// @desc    Get all criminals with search and filtering
// @route   GET /api/criminals
// @access  Private
const getCriminals = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      gender,
      city,
      gang,
      riskLevel,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query object
    const query = { isActive: true };

    // Search functionality
    if (search) {
      query.$or = [
        { fullName: { $regex: search, $options: 'i' } },
        { alias: { $regex: search, $options: 'i' } },
        { nationalId: { $regex: search, $options: 'i' } }
      ];
    }

    // Filters
    if (gender && gender !== 'all') {
      query.gender = gender;
    }

    if (city) {
      query['knownAddresses.city'] = { $regex: city, $options: 'i' };
    }

    if (gang) {
      query.gangAffiliation = { $regex: gang, $options: 'i' };
    }

    if (riskLevel && riskLevel !== 'all') {
      query.riskLevel = riskLevel;
    }

    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Sort options
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query
    const criminals = await Criminal.find(query)
      .populate('crimeCount')
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get total count for pagination
    const total = await Criminal.countDocuments(query);

    res.status(200).json({
      success: true,
      count: criminals.length,
      total,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      },
      data: criminals
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single criminal by ID
// @route   GET /api/criminals/:id
// @access  Private
const getCriminal = async (req, res, next) => {
  try {
    const criminal = await Criminal.findById(req.params.id)
      .populate('crimeCount');

    if (!criminal || !criminal.isActive) {
      return res.status(404).json({
        success: false,
        error: 'Criminal not found'
      });
    }

    // Get crime records for this criminal
    const crimeRecords = await CrimeRecord.find({ 
      criminalId: req.params.id,
      isActive: true 
    }).sort({ date: -1 });

    res.status(200).json({
      success: true,
      data: {
        criminal,
        crimeRecords
      }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create new criminal
// @route   POST /api/criminals
// @access  Private
const createCriminal = async (req, res, next) => {
  try {
    // Add admin info to the criminal data
    req.body.createdBy = req.admin.fullName;
    req.body.lastModifiedBy = req.admin.fullName;

    const criminal = await Criminal.create(req.body);

    res.status(201).json({
      success: true,
      data: criminal
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update criminal
// @route   PUT /api/criminals/:id
// @access  Private
const updateCriminal = async (req, res, next) => {
  try {
    // Add admin info
    req.body.lastModifiedBy = req.admin.fullName;

    const criminal = await Criminal.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true
      }
    );

    if (!criminal) {
      return res.status(404).json({
        success: false,
        error: 'Criminal not found'
      });
    }

    res.status(200).json({
      success: true,
      data: criminal
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete criminal (soft delete)
// @route   DELETE /api/criminals/:id
// @access  Private
const deleteCriminal = async (req, res, next) => {
  try {
    const criminal = await Criminal.findByIdAndUpdate(
      req.params.id,
      { 
        isActive: false,
        lastModifiedBy: req.admin.fullName
      },
      { new: true }
    );

    if (!criminal) {
      return res.status(404).json({
        success: false,
        error: 'Criminal not found'
      });
    }

    // Also soft delete associated crime records
    await CrimeRecord.updateMany(
      { criminalId: req.params.id },
      { 
        isActive: false,
        lastModifiedBy: req.admin.fullName
      }
    );

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Search criminals by advanced criteria
// @route   POST /api/criminals/search
// @access  Private
const advancedSearch = async (req, res, next) => {
  try {
    const {
      fullName,
      alias,
      nationalId,
      gender,
      ageRange,
      addresses,
      vehicles,
      gangAffiliation,
      riskLevel,
      crimeTypes,
      dateRange,
      page = 1,
      limit = 10
    } = req.body;

    const query = { isActive: true };
    const pipeline = [];

    // Text search
    if (fullName) {
      query.fullName = { $regex: fullName, $options: 'i' };
    }
    if (alias) {
      query.alias = { $regex: alias, $options: 'i' };
    }
    if (nationalId) {
      query.nationalId = nationalId;
    }

    // Basic filters
    if (gender) query.gender = gender;
    if (gangAffiliation) {
      query.gangAffiliation = { $regex: gangAffiliation, $options: 'i' };
    }
    if (riskLevel) query.riskLevel = riskLevel;

    // Age range filter
    if (ageRange && (ageRange.min || ageRange.max)) {
      const today = new Date();
      if (ageRange.max) {
        const minBirthDate = new Date(today.getFullYear() - ageRange.max - 1, today.getMonth(), today.getDate());
        query.birthDate = { $gte: minBirthDate };
      }
      if (ageRange.min) {
        const maxBirthDate = new Date(today.getFullYear() - ageRange.min, today.getMonth(), today.getDate());
        query.birthDate = { ...query.birthDate, $lte: maxBirthDate };
      }
    }

    // Address search
    if (addresses && addresses.length > 0) {
      query.$or = addresses.map(addr => ({
        'knownAddresses.city': { $regex: addr, $options: 'i' }
      }));
    }

    // Vehicle search
    if (vehicles && vehicles.length > 0) {
      const vehicleQueries = vehicles.map(vehicle => {
        const vehicleQuery = {};
        if (vehicle.make) vehicleQuery['knownVehicles.make'] = { $regex: vehicle.make, $options: 'i' };
        if (vehicle.model) vehicleQuery['knownVehicles.model'] = { $regex: vehicle.model, $options: 'i' };
        if (vehicle.licensePlate) vehicleQuery['knownVehicles.licensePlate'] = { $regex: vehicle.licensePlate, $options: 'i' };
        return vehicleQuery;
      });
      
      if (query.$or) {
        query.$and = [{ $or: query.$or }, { $or: vehicleQueries }];
        delete query.$or;
      } else {
        query.$or = vehicleQueries;
      }
    }

    // Start aggregation pipeline
    pipeline.push({ $match: query });

    // Join with crime records if crime-related filters are specified
    if (crimeTypes && crimeTypes.length > 0 || dateRange) {
      pipeline.push({
        $lookup: {
          from: 'crimerecords',
          localField: '_id',
          foreignField: 'criminalId',
          as: 'crimes'
        }
      });

      const crimeMatch = { 'crimes.isActive': true };
      
      if (crimeTypes && crimeTypes.length > 0) {
        crimeMatch['crimes.type'] = { $in: crimeTypes };
      }

      if (dateRange) {
        if (dateRange.start) crimeMatch['crimes.date'] = { $gte: new Date(dateRange.start) };
        if (dateRange.end) {
          crimeMatch['crimes.date'] = { 
            ...crimeMatch['crimes.date'], 
            $lte: new Date(dateRange.end) 
          };
        }
      }

      pipeline.push({ $match: crimeMatch });
    }

    // Add pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    pipeline.push({ $skip: skip });
    pipeline.push({ $limit: parseInt(limit) });

    // Execute aggregation
    const criminals = await Criminal.aggregate(pipeline);

    // Get total count
    const countPipeline = [...pipeline.slice(0, -2)]; // Remove skip and limit
    countPipeline.push({ $count: 'total' });
    const countResult = await Criminal.aggregate(countPipeline);
    const total = countResult.length > 0 ? countResult[0].total : 0;

    res.status(200).json({
      success: true,
      count: criminals.length,
      total,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      },
      data: criminals
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getCriminals,
  getCriminal,
  createCriminal,
  updateCriminal,
  deleteCriminal,
  advancedSearch
};
