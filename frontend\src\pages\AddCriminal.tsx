import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { criminalAPI } from '../utils/api';

interface Address {
  address: string;
  city: string;
  state: string;
  zipCode: string;
}

interface Vehicle {
  make: string;
  model: string;
  year: number | '';
  color: string;
  licensePlate: string;
}

interface CriminalForm {
  fullName: string;
  alias: string;
  nationalId: string;
  gender: string;
  birthDate: string;
  photoUrl: string;
  distinctiveMarks: string;
  fingerprintId: string;
  knownAddresses: Address[];
  knownVehicles: Vehicle[];
  gangAffiliation: string;
  riskLevel: string;
  notes: string;
}

const AddCriminal = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState<CriminalForm>({
    fullName: '',
    alias: '',
    nationalId: '',
    gender: '',
    birthDate: '',
    photoUrl: '',
    distinctiveMarks: '',
    fingerprintId: '',
    knownAddresses: [{ address: '', city: '', state: '', zipCode: '' }],
    knownVehicles: [{ make: '', model: '', year: '', color: '', licensePlate: '' }],
    gangAffiliation: '',
    riskLevel: 'Low',
    notes: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAddressChange = (index: number, field: keyof Address, value: string) => {
    setFormData(prev => ({
      ...prev,
      knownAddresses: prev.knownAddresses.map((addr, i) =>
        i === index ? { ...addr, [field]: value } : addr
      )
    }));
  };

  const handleVehicleChange = (index: number, field: keyof Vehicle, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      knownVehicles: prev.knownVehicles.map((vehicle, i) =>
        i === index ? { ...vehicle, [field]: value } : vehicle
      )
    }));
  };

  const addAddress = () => {
    setFormData(prev => ({
      ...prev,
      knownAddresses: [...prev.knownAddresses, { address: '', city: '', state: '', zipCode: '' }]
    }));
  };

  const removeAddress = (index: number) => {
    setFormData(prev => ({
      ...prev,
      knownAddresses: prev.knownAddresses.filter((_, i) => i !== index)
    }));
  };

  const addVehicle = () => {
    setFormData(prev => ({
      ...prev,
      knownVehicles: [...prev.knownVehicles, { make: '', model: '', year: '', color: '', licensePlate: '' }]
    }));
  };

  const removeVehicle = (index: number) => {
    setFormData(prev => ({
      ...prev,
      knownVehicles: prev.knownVehicles.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Clean up the data before sending
      const cleanedData = {
        ...formData,
        alias: formData.alias || undefined,
        nationalId: formData.nationalId || undefined,
        birthDate: formData.birthDate || undefined,
        photoUrl: formData.photoUrl || undefined,
        distinctiveMarks: formData.distinctiveMarks || undefined,
        fingerprintId: formData.fingerprintId || undefined,
        gangAffiliation: formData.gangAffiliation || undefined,
        notes: formData.notes || undefined,
        knownAddresses: formData.knownAddresses.filter(addr =>
          addr.address.trim() && addr.city.trim()
        ),
        knownVehicles: formData.knownVehicles.filter(vehicle =>
          vehicle.make.trim() && vehicle.model.trim()
        ).map(vehicle => ({
          ...vehicle,
          year: vehicle.year ? Number(vehicle.year) : undefined
        }))
      };

      await criminalAPI.create(cleanedData);
      navigate('/criminals');
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to create criminal record');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Add Criminal</h1>
        <p className="text-gray-600">Register a new criminal record</p>
      </div>

      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-700">{error}</div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Personal Information */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium">Personal Information</h3>
          </div>
          <div className="card-content">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name *
                </label>
                <input
                  type="text"
                  name="fullName"
                  required
                  className="input"
                  value={formData.fullName}
                  onChange={handleInputChange}
                  placeholder="Enter full name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Alias
                </label>
                <input
                  type="text"
                  name="alias"
                  className="input"
                  value={formData.alias}
                  onChange={handleInputChange}
                  placeholder="Known alias or nickname"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  National ID
                </label>
                <input
                  type="text"
                  name="nationalId"
                  className="input"
                  value={formData.nationalId}
                  onChange={handleInputChange}
                  placeholder="National ID number"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Gender *
                </label>
                <select
                  name="gender"
                  required
                  className="input"
                  value={formData.gender}
                  onChange={handleInputChange}
                >
                  <option value="">Select gender</option>
                  <option value="Male">Male</option>
                  <option value="Female">Female</option>
                  <option value="Other">Other</option>
                  <option value="Unknown">Unknown</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Birth Date
                </label>
                <input
                  type="date"
                  name="birthDate"
                  className="input"
                  value={formData.birthDate}
                  onChange={handleInputChange}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Risk Level *
                </label>
                <select
                  name="riskLevel"
                  required
                  className="input"
                  value={formData.riskLevel}
                  onChange={handleInputChange}
                >
                  <option value="Low">Low</option>
                  <option value="Medium">Medium</option>
                  <option value="High">High</option>
                  <option value="Critical">Critical</option>
                </select>
              </div>
            </div>

            <div className="mt-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Photo URL
                </label>
                <input
                  type="url"
                  name="photoUrl"
                  className="input"
                  value={formData.photoUrl}
                  onChange={handleInputChange}
                  placeholder="https://example.com/photo.jpg"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Distinctive Marks
                </label>
                <textarea
                  name="distinctiveMarks"
                  rows={3}
                  className="input"
                  value={formData.distinctiveMarks}
                  onChange={handleInputChange}
                  placeholder="Scars, tattoos, birthmarks, etc."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Fingerprint ID
                </label>
                <input
                  type="text"
                  name="fingerprintId"
                  className="input"
                  value={formData.fingerprintId}
                  onChange={handleInputChange}
                  placeholder="Fingerprint identification number"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Gang Affiliation
                </label>
                <input
                  type="text"
                  name="gangAffiliation"
                  className="input"
                  value={formData.gangAffiliation}
                  onChange={handleInputChange}
                  placeholder="Known gang or criminal organization"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Known Addresses */}
        <div className="card">
          <div className="card-header">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Known Addresses</h3>
              <button
                type="button"
                onClick={addAddress}
                className="btn-outline"
              >
                Add Address
              </button>
            </div>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              {formData.knownAddresses.map((address, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h4 className="font-medium text-gray-900">Address {index + 1}</h4>
                    {formData.knownAddresses.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeAddress(index)}
                        className="text-red-600 hover:text-red-800 text-sm"
                      >
                        Remove
                      </button>
                    )}
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Street Address
                      </label>
                      <input
                        type="text"
                        className="input"
                        value={address.address}
                        onChange={(e) => handleAddressChange(index, 'address', e.target.value)}
                        placeholder="123 Main Street"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        City
                      </label>
                      <input
                        type="text"
                        className="input"
                        value={address.city}
                        onChange={(e) => handleAddressChange(index, 'city', e.target.value)}
                        placeholder="New York"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        State
                      </label>
                      <input
                        type="text"
                        className="input"
                        value={address.state}
                        onChange={(e) => handleAddressChange(index, 'state', e.target.value)}
                        placeholder="NY"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        ZIP Code
                      </label>
                      <input
                        type="text"
                        className="input"
                        value={address.zipCode}
                        onChange={(e) => handleAddressChange(index, 'zipCode', e.target.value)}
                        placeholder="10001"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Known Vehicles */}
        <div className="card">
          <div className="card-header">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Known Vehicles</h3>
              <button
                type="button"
                onClick={addVehicle}
                className="btn-outline"
              >
                Add Vehicle
              </button>
            </div>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              {formData.knownVehicles.map((vehicle, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h4 className="font-medium text-gray-900">Vehicle {index + 1}</h4>
                    {formData.knownVehicles.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeVehicle(index)}
                        className="text-red-600 hover:text-red-800 text-sm"
                      >
                        Remove
                      </button>
                    )}
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Make
                      </label>
                      <input
                        type="text"
                        className="input"
                        value={vehicle.make}
                        onChange={(e) => handleVehicleChange(index, 'make', e.target.value)}
                        placeholder="Toyota"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Model
                      </label>
                      <input
                        type="text"
                        className="input"
                        value={vehicle.model}
                        onChange={(e) => handleVehicleChange(index, 'model', e.target.value)}
                        placeholder="Camry"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Year
                      </label>
                      <input
                        type="number"
                        className="input"
                        value={vehicle.year}
                        onChange={(e) => handleVehicleChange(index, 'year', e.target.value ? parseInt(e.target.value) : '')}
                        placeholder="2020"
                        min="1900"
                        max={new Date().getFullYear() + 1}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Color
                      </label>
                      <input
                        type="text"
                        className="input"
                        value={vehicle.color}
                        onChange={(e) => handleVehicleChange(index, 'color', e.target.value)}
                        placeholder="Blue"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        License Plate
                      </label>
                      <input
                        type="text"
                        className="input"
                        value={vehicle.licensePlate}
                        onChange={(e) => handleVehicleChange(index, 'licensePlate', e.target.value.toUpperCase())}
                        placeholder="ABC123"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Additional Notes */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium">Additional Notes</h3>
          </div>
          <div className="card-content">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <textarea
                name="notes"
                rows={4}
                className="input"
                value={formData.notes}
                onChange={handleInputChange}
                placeholder="Any additional information about the criminal..."
              />
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => navigate('/criminals')}
            className="btn-outline"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn-primary"
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="flex items-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Creating...
              </div>
            ) : (
              'Create Criminal Record'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AddCriminal;
