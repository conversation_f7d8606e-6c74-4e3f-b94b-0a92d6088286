@import "tailwindcss";

/* Custom component styles */
.btn {
  @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  @apply disabled:opacity-50 disabled:pointer-events-none;
}

.btn-primary {
  @apply btn bg-blue-600 text-white hover:bg-blue-700 h-10 py-2 px-4;
}

.btn-secondary {
  @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 h-10 py-2 px-4;
}

.btn-outline {
  @apply btn border border-gray-300 hover:bg-gray-50 h-10 py-2 px-4;
}

.btn-ghost {
  @apply btn hover:bg-gray-100 h-10 py-2 px-4;
}

.btn-danger {
  @apply btn bg-red-600 text-white hover:bg-red-700 h-10 py-2 px-4;
}

.input {
  @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm;
  @apply placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  @apply disabled:cursor-not-allowed disabled:opacity-50;
}

.card {
  @apply rounded-lg border border-gray-200 bg-white shadow-sm;
}

.card-header {
  @apply flex flex-col space-y-1.5 p-6;
}

.card-content {
  @apply p-6 pt-0;
}

.card-footer {
  @apply flex items-center p-6 pt-0;
}

.table {
  @apply w-full border-collapse;
}

.table th {
  @apply border-b border-gray-200 bg-gray-50 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table td {
  @apply border-b border-gray-200 px-4 py-3 text-sm text-gray-900;
}

.badge {
  @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
}

.badge-success {
  @apply badge bg-green-100 text-green-800;
}

.badge-warning {
  @apply badge bg-yellow-100 text-yellow-800;
}

.badge-danger {
  @apply badge bg-red-100 text-red-800;
}

.badge-info {
  @apply badge bg-blue-100 text-blue-800;
}
