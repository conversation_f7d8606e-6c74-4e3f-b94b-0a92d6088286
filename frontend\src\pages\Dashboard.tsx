import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { statsAPI } from '../utils/api';

interface DashboardStats {
  overview: {
    totalCriminals: number;
    totalCrimeRecords: number;
    totalAdmins: number;
    recentCriminals: number;
    recentCrimeRecords: number;
    recentArrests: number;
  };
  crimeTypeStats: Array<{
    _id: string;
    count: number;
  }>;
  riskLevelStats: Array<{
    _id: string;
    count: number;
  }>;
  monthlyTrends: Array<{
    _id: {
      year: number;
      month: number;
    };
    count: number;
  }>;
  topCities: Array<{
    _id: string;
    count: number;
  }>;
}

const Dashboard = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await statsAPI.getDashboard();
        setStats(response.data.data);
      } catch (err: any) {
        setError(err.response?.data?.error || 'Failed to load dashboard statistics');
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="text-sm text-red-700">{error}</div>
      </div>
    );
  }

  if (!stats) return null;

  const { overview, crimeTypeStats, riskLevelStats, monthlyTrends, topCities } = stats;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Overview of criminal records and activities</p>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="card">
          <div className="card-content">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                  <span className="text-blue-600 text-lg">👤</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Criminals</p>
                <p className="text-2xl font-bold text-gray-900">{overview.totalCriminals}</p>
                <p className="text-sm text-green-600">+{overview.recentCriminals} this month</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-red-100 rounded-md flex items-center justify-center">
                  <span className="text-red-600 text-lg">📋</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Crime Records</p>
                <p className="text-2xl font-bold text-gray-900">{overview.totalCrimeRecords}</p>
                <p className="text-sm text-green-600">+{overview.recentCrimeRecords} this month</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                  <span className="text-green-600 text-lg">🚔</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Recent Arrests</p>
                <p className="text-2xl font-bold text-gray-900">{overview.recentArrests}</p>
                <p className="text-sm text-gray-500">Last 7 days</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts and Statistics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Crime Types */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium">Crime Types</h3>
          </div>
          <div className="card-content">
            <div className="space-y-3">
              {crimeTypeStats.slice(0, 5).map((stat, index) => (
                <div key={stat._id} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">{stat._id}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{
                          width: `${(stat.count / crimeTypeStats[0].count) * 100}%`
                        }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-900 w-8 text-right">
                      {stat.count}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Risk Levels */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium">Risk Levels</h3>
          </div>
          <div className="card-content">
            <div className="space-y-3">
              {riskLevelStats.map((stat) => {
                const colors = {
                  Low: 'bg-green-500',
                  Medium: 'bg-yellow-500',
                  High: 'bg-orange-500',
                  Critical: 'bg-red-500'
                };
                return (
                  <div key={stat._id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${colors[stat._id as keyof typeof colors] || 'bg-gray-500'}`}></div>
                      <span className="text-sm text-gray-600">{stat._id}</span>
                    </div>
                    <span className="text-sm font-medium text-gray-900">{stat.count}</span>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Top Cities */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium">Crime Hotspots</h3>
        </div>
        <div className="card-content">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {topCities.slice(0, 6).map((city, index) => (
              <div key={city._id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium text-gray-900">{city._id}</p>
                  <p className="text-sm text-gray-500">#{index + 1} most active</p>
                </div>
                <span className="text-lg font-bold text-blue-600">{city.count}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium">Quick Actions</h3>
        </div>
        <div className="card-content">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link
              to="/criminals/add"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center mr-3">
                <span className="text-blue-600">➕</span>
              </div>
              <div>
                <p className="font-medium text-gray-900">Add Criminal</p>
                <p className="text-sm text-gray-500">Register new criminal</p>
              </div>
            </Link>

            <Link
              to="/crime-records/add"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="w-8 h-8 bg-red-100 rounded-md flex items-center justify-center mr-3">
                <span className="text-red-600">📝</span>
              </div>
              <div>
                <p className="font-medium text-gray-900">Add Crime Record</p>
                <p className="text-sm text-gray-500">Log new incident</p>
              </div>
            </Link>

            <Link
              to="/criminals"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center mr-3">
                <span className="text-green-600">🔍</span>
              </div>
              <div>
                <p className="font-medium text-gray-900">Search Criminals</p>
                <p className="text-sm text-gray-500">Find records</p>
              </div>
            </Link>

            <Link
              to="/statistics"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center mr-3">
                <span className="text-purple-600">📊</span>
              </div>
              <div>
                <p className="font-medium text-gray-900">View Statistics</p>
                <p className="text-sm text-gray-500">Analyze data</p>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
