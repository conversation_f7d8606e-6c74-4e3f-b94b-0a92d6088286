import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { criminalAPI } from '../utils/api';

interface Criminal {
  _id: string;
  fullName: string;
  alias?: string;
  nationalId?: string;
  gender: 'Male' | 'Female' | 'Other' | 'Unknown';
  birthDate?: string;
  age?: number;
  photoUrl?: string;
  distinctiveMarks?: string;
  knownAddresses: Array<{
    address: string;
    city: string;
    state?: string;
    isActive: boolean;
  }>;
  knownVehicles: Array<{
    make: string;
    model: string;
    year?: number;
    color?: string;
    licensePlate?: string;
    isActive: boolean;
  }>;
  gangAffiliation?: string;
  riskLevel: 'Low' | 'Medium' | 'High' | 'Critical';
  crimeCount?: number;
  createdAt: string;
  updatedAt: string;
}

interface PaginatedResponse {
  success: boolean;
  count: number;
  total: number;
  pagination: {
    page: number;
    limit: number;
    pages: number;
  };
  data: Criminal[];
}

const CriminalList = () => {
  const [criminals, setCriminals] = useState<Criminal[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    gender: 'all',
    riskLevel: 'all',
    city: '',
    gang: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState('desc');

  const fetchCriminals = async () => {
    try {
      setIsLoading(true);
      const params = {
        page: pagination.page,
        limit: pagination.limit,
        search: searchTerm || undefined,
        gender: filters.gender !== 'all' ? filters.gender : undefined,
        riskLevel: filters.riskLevel !== 'all' ? filters.riskLevel : undefined,
        city: filters.city || undefined,
        gang: filters.gang || undefined,
        sortBy,
        sortOrder
      };

      const response = await criminalAPI.getAll(params);
      const data: PaginatedResponse = response.data;

      setCriminals(data.data);
      setPagination({
        page: data.pagination.page,
        limit: data.pagination.limit,
        total: data.total,
        pages: data.pagination.pages
      });
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to load criminals');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCriminals();
  }, [pagination.page, searchTerm, filters, sortBy, sortOrder]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchCriminals();
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'Low': return 'badge-success';
      case 'Medium': return 'badge-warning';
      case 'High': return 'badge-danger';
      case 'Critical': return 'bg-red-600 text-white';
      default: return 'badge-info';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Criminal Records</h1>
          <p className="text-gray-600">Manage and search criminal records</p>
        </div>
        <div className="rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-700">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Criminal Records</h1>
          <p className="text-gray-600">Manage and search criminal records</p>
        </div>
        <Link
          to="/criminals/add"
          className="btn-primary"
        >
          Add New Criminal
        </Link>
      </div>

      {/* Search and Filters */}
      <div className="card">
        <div className="card-content">
          <form onSubmit={handleSearch} className="space-y-4">
            {/* Search Bar */}
            <div className="flex gap-4">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="Search by name, alias, or national ID..."
                  className="input"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <button type="submit" className="btn-primary">
                Search
              </button>
            </div>

            {/* Filters */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Gender
                </label>
                <select
                  className="input"
                  value={filters.gender}
                  onChange={(e) => handleFilterChange('gender', e.target.value)}
                >
                  <option value="all">All Genders</option>
                  <option value="Male">Male</option>
                  <option value="Female">Female</option>
                  <option value="Other">Other</option>
                  <option value="Unknown">Unknown</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Risk Level
                </label>
                <select
                  className="input"
                  value={filters.riskLevel}
                  onChange={(e) => handleFilterChange('riskLevel', e.target.value)}
                >
                  <option value="all">All Risk Levels</option>
                  <option value="Low">Low</option>
                  <option value="Medium">Medium</option>
                  <option value="High">High</option>
                  <option value="Critical">Critical</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  City
                </label>
                <input
                  type="text"
                  placeholder="Filter by city..."
                  className="input"
                  value={filters.city}
                  onChange={(e) => handleFilterChange('city', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Gang Affiliation
                </label>
                <input
                  type="text"
                  placeholder="Filter by gang..."
                  className="input"
                  value={filters.gang}
                  onChange={(e) => handleFilterChange('gang', e.target.value)}
                />
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Results Summary */}
      <div className="flex justify-between items-center">
        <p className="text-sm text-gray-700">
          Showing {criminals.length} of {pagination.total} criminals
        </p>
        <div className="flex items-center space-x-2">
          <label className="text-sm text-gray-700">Sort by:</label>
          <select
            className="input w-auto"
            value={`${sortBy}-${sortOrder}`}
            onChange={(e) => {
              const [field, order] = e.target.value.split('-');
              setSortBy(field);
              setSortOrder(order);
            }}
          >
            <option value="createdAt-desc">Newest First</option>
            <option value="createdAt-asc">Oldest First</option>
            <option value="fullName-asc">Name A-Z</option>
            <option value="fullName-desc">Name Z-A</option>
            <option value="riskLevel-desc">Risk Level High-Low</option>
            <option value="riskLevel-asc">Risk Level Low-High</option>
          </select>
        </div>
      </div>

      {/* Criminal List */}
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      ) : criminals.length === 0 ? (
        <div className="card">
          <div className="card-content">
            <div className="text-center py-8">
              <div className="text-gray-400 text-6xl mb-4">👤</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No criminals found</h3>
              <p className="text-gray-500 mb-4">
                {searchTerm || Object.values(filters).some(f => f && f !== 'all')
                  ? 'Try adjusting your search criteria or filters.'
                  : 'Get started by adding your first criminal record.'}
              </p>
              <Link to="/criminals/add" className="btn-primary">
                Add New Criminal
              </Link>
            </div>
          </div>
        </div>
      ) : (
        <div className="card">
          <div className="overflow-x-auto">
            <table className="table">
              <thead>
                <tr>
                  <th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('fullName')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Name</span>
                      {sortBy === 'fullName' && (
                        <span className="text-blue-600">
                          {sortOrder === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th>Alias</th>
                  <th>National ID</th>
                  <th>Gender</th>
                  <th>Age</th>
                  <th>Location</th>
                  <th>Gang</th>
                  <th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('riskLevel')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Risk Level</span>
                      {sortBy === 'riskLevel' && (
                        <span className="text-blue-600">
                          {sortOrder === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th>Crime Count</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {criminals.map((criminal) => (
                  <tr key={criminal._id} className="hover:bg-gray-50">
                    <td>
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          {criminal.photoUrl ? (
                            <img
                              className="h-10 w-10 rounded-full object-cover"
                              src={criminal.photoUrl}
                              alt={criminal.fullName}
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                              <span className="text-gray-600 font-medium">
                                {criminal.fullName.charAt(0)}
                              </span>
                            </div>
                          )}
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">
                            {criminal.fullName}
                          </div>
                          <div className="text-sm text-gray-500">
                            Added {formatDate(criminal.createdAt)}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td>
                      {criminal.alias ? (
                        <span className="text-gray-900">{criminal.alias}</span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    <td>
                      {criminal.nationalId ? (
                        <span className="font-mono text-sm">{criminal.nationalId}</span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    <td>
                      <span className="text-gray-900">{criminal.gender}</span>
                    </td>
                    <td>
                      {criminal.age ? (
                        <span className="text-gray-900">{criminal.age}</span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    <td>
                      {criminal.knownAddresses.length > 0 ? (
                        <div className="text-sm">
                          <div className="text-gray-900">
                            {criminal.knownAddresses[0].city}
                          </div>
                          {criminal.knownAddresses.length > 1 && (
                            <div className="text-gray-500">
                              +{criminal.knownAddresses.length - 1} more
                            </div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    <td>
                      {criminal.gangAffiliation ? (
                        <span className="text-gray-900">{criminal.gangAffiliation}</span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    <td>
                      <span className={`badge ${getRiskLevelColor(criminal.riskLevel)}`}>
                        {criminal.riskLevel}
                      </span>
                    </td>
                    <td>
                      <span className="text-gray-900">{criminal.crimeCount || 0}</span>
                    </td>
                    <td>
                      <div className="flex items-center space-x-2">
                        <Link
                          to={`/criminals/${criminal._id}`}
                          className="text-blue-600 hover:text-blue-900 text-sm font-medium"
                        >
                          View
                        </Link>
                        <span className="text-gray-300">|</span>
                        <Link
                          to={`/crime-records/add?criminalId=${criminal._id}`}
                          className="text-green-600 hover:text-green-900 text-sm font-medium"
                        >
                          Add Crime
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Page {pagination.page} of {pagination.pages}
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
              disabled={pagination.page === 1}
              className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>

            {/* Page numbers */}
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                const pageNum = i + 1;
                const isActive = pageNum === pagination.page;
                return (
                  <button
                    key={pageNum}
                    onClick={() => setPagination(prev => ({ ...prev, page: pageNum }))}
                    className={`px-3 py-1 text-sm rounded-md ${
                      isActive
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              })}
            </div>

            <button
              onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.pages, prev.page + 1) }))}
              disabled={pagination.page === pagination.pages}
              className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default CriminalList;
