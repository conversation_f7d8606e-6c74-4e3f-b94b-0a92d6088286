import { useState, useEffect } from 'react';
import { statsAPI } from '../utils/api';

interface CriminalStats {
  ageStats: Array<{
    _id: string | number;
    count: number;
  }>;
  genderStats: Array<{
    _id: string;
    count: number;
  }>;
  gangStats: Array<{
    _id: string;
    count: number;
  }>;
  activeCriminals: Array<{
    criminalId: string;
    crimeCount: number;
    latestCrime: string;
    crimeTypes: string[];
    fullName: string;
    alias?: string;
    riskLevel: string;
  }>;
}

interface LocationStats {
  cityStats: Array<{
    _id: string;
    crimeCount: number;
    crimeTypes: string[];
    latestCrime: string;
  }>;
  criminalLocationStats: Array<{
    _id: string;
    criminalCount: number;
  }>;
}

const Statistics = () => {
  const [criminalStats, setCriminalStats] = useState<CriminalStats | null>(null);
  const [locationStats, setLocationStats] = useState<LocationStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('criminals');

  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        setIsLoading(true);
        const [criminalResponse, locationResponse] = await Promise.all([
          statsAPI.getCriminals(),
          statsAPI.getLocations()
        ]);

        setCriminalStats(criminalResponse.data.data);
        setLocationStats(locationResponse.data.data);
      } catch (err: any) {
        setError(err.response?.data?.error || 'Failed to load statistics');
      } finally {
        setIsLoading(false);
      }
    };

    fetchStatistics();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Statistics</h1>
          <p className="text-gray-600">Analyze criminal activity patterns and trends</p>
        </div>
        <div className="rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-700">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Statistics</h1>
        <p className="text-gray-600">Analyze criminal activity patterns and trends</p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'criminals', name: 'Criminal Analytics' },
            { id: 'locations', name: 'Location Analytics' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Criminal Analytics Tab */}
      {activeTab === 'criminals' && criminalStats && (
        <div className="space-y-6">
          {/* Age Distribution */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium">Age Distribution</h3>
            </div>
            <div className="card-content">
              <div className="space-y-3">
                {criminalStats.ageStats.map((stat, index) => {
                  const maxCount = Math.max(...criminalStats.ageStats.map(s => s.count));
                  const percentage = (stat.count / maxCount) * 100;

                  return (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">
                        {typeof stat._id === 'number' ? `${stat._id}-${stat._id + 9}` : stat._id} years
                      </span>
                      <div className="flex items-center space-x-2 flex-1 ml-4">
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium text-gray-900 w-8 text-right">
                          {stat.count}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Gender Distribution */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium">Gender Distribution</h3>
            </div>
            <div className="card-content">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {criminalStats.genderStats.map((stat) => (
                  <div key={stat._id} className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{stat.count}</div>
                    <div className="text-sm text-gray-600">{stat._id}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Gang Affiliations */}
          {criminalStats.gangStats.length > 0 && (
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium">Gang Affiliations</h3>
              </div>
              <div className="card-content">
                <div className="space-y-3">
                  {criminalStats.gangStats.slice(0, 10).map((stat, index) => {
                    const maxCount = Math.max(...criminalStats.gangStats.map(s => s.count));
                    const percentage = (stat.count / maxCount) * 100;

                    return (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">{stat._id}</span>
                        <div className="flex items-center space-x-2 flex-1 ml-4">
                          <div className="flex-1 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-red-600 h-2 rounded-full"
                              style={{ width: `${percentage}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium text-gray-900 w-8 text-right">
                            {stat.count}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Most Active Criminals */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium">Most Active Criminals</h3>
            </div>
            <div className="card-content">
              <div className="space-y-4">
                {criminalStats.activeCriminals.slice(0, 10).map((criminal, index) => (
                  <div key={criminal.criminalId} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 text-sm font-medium">#{index + 1}</span>
                        </div>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{criminal.fullName}</div>
                        {criminal.alias && (
                          <div className="text-sm text-gray-500">Alias: {criminal.alias}</div>
                        )}
                        <div className="text-sm text-gray-500">
                          Latest: {formatDate(criminal.latestCrime)}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-gray-900">{criminal.crimeCount}</div>
                      <div className="text-sm text-gray-500">crimes</div>
                      <div className={`text-xs px-2 py-1 rounded-full ${
                        criminal.riskLevel === 'Critical' ? 'bg-red-100 text-red-800' :
                        criminal.riskLevel === 'High' ? 'bg-orange-100 text-orange-800' :
                        criminal.riskLevel === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {criminal.riskLevel}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Location Analytics Tab */}
      {activeTab === 'locations' && locationStats && (
        <div className="space-y-6">
          {/* Crime Hotspots */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium">Crime Hotspots by City</h3>
            </div>
            <div className="card-content">
              <div className="space-y-4">
                {locationStats.cityStats.slice(0, 15).map((city, index) => (
                  <div key={city._id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                          <span className="text-red-600 text-sm font-medium">#{index + 1}</span>
                        </div>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{city._id}</div>
                        <div className="text-sm text-gray-500">
                          Latest: {formatDate(city.latestCrime)}
                        </div>
                        <div className="text-sm text-gray-500">
                          Types: {city.crimeTypes.slice(0, 3).join(', ')}
                          {city.crimeTypes.length > 3 && ` +${city.crimeTypes.length - 3} more`}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-gray-900">{city.crimeCount}</div>
                      <div className="text-sm text-gray-500">crimes</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Criminal Distribution by Location */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium">Criminal Distribution by City</h3>
            </div>
            <div className="card-content">
              <div className="space-y-3">
                {locationStats.criminalLocationStats.slice(0, 15).map((location, index) => {
                  const maxCount = Math.max(...locationStats.criminalLocationStats.map(l => l.criminalCount));
                  const percentage = (location.criminalCount / maxCount) * 100;

                  return (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">{location._id}</span>
                      <div className="flex items-center space-x-2 flex-1 ml-4">
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-green-600 h-2 rounded-full"
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium text-gray-900 w-8 text-right">
                          {location.criminalCount}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Location Comparison */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium">Top Cities Comparison</h3>
            </div>
            <div className="card-content">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {locationStats.cityStats.slice(0, 6).map((city, index) => (
                  <div key={city._id} className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{city._id}</h4>
                      <span className="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded-full">
                        #{index + 1}
                      </span>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">Total Crimes:</span>
                        <span className="font-medium text-gray-900">{city.crimeCount}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">Crime Types:</span>
                        <span className="font-medium text-gray-900">{city.crimeTypes.length}</span>
                      </div>
                      <div className="text-xs text-gray-500">
                        Latest: {formatDate(city.latestCrime)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Summary Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="card">
              <div className="card-content">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">
                    {locationStats.cityStats.length}
                  </div>
                  <div className="text-sm text-gray-500">Cities with Crime Activity</div>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="card-content">
                <div className="text-center">
                  <div className="text-3xl font-bold text-red-600">
                    {locationStats.cityStats.reduce((sum, city) => sum + city.crimeCount, 0)}
                  </div>
                  <div className="text-sm text-gray-500">Total Crimes Recorded</div>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="card-content">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">
                    {locationStats.criminalLocationStats.reduce((sum, loc) => sum + loc.criminalCount, 0)}
                  </div>
                  <div className="text-sm text-gray-500">Total Criminal Addresses</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Statistics;
