const jwt = require('jsonwebtoken');
const { Admin } = require('../models');

// Protect routes - require authentication
const protect = async (req, res, next) => {
  try {
    let token;

    // Check for token in headers
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    // Make sure token exists
    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to access this route'
      });
    }

    try {
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);

      // Check if admin still exists
      const admin = await Admin.findById(decoded.id).select('+password');
      if (!admin) {
        return res.status(401).json({
          success: false,
          error: 'The admin belonging to this token no longer exists'
        });
      }

      // Check if admin is active
      if (!admin.isActive) {
        return res.status(401).json({
          success: false,
          error: 'Admin account has been deactivated'
        });
      }

      // Check if admin changed password after the token was issued
      if (admin.changedPasswordAfter(decoded.iat)) {
        return res.status(401).json({
          success: false,
          error: 'Admin recently changed password. Please log in again'
        });
      }

      // Grant access to protected route
      req.admin = admin;
      next();
    } catch (error) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to access this route'
      });
    }
  } catch (error) {
    next(error);
  }
};

// Grant access to specific ranks
const authorize = (...ranks) => {
  return (req, res, next) => {
    if (!ranks.includes(req.admin.rank)) {
      return res.status(403).json({
        success: false,
        error: `Admin rank ${req.admin.rank} is not authorized to access this route`
      });
    }
    next();
  };
};

// Optional authentication - doesn't require token but adds admin to req if present
const optionalAuth = async (req, res, next) => {
  try {
    let token;

    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    if (token) {
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const admin = await Admin.findById(decoded.id);
        
        if (admin && admin.isActive && !admin.changedPasswordAfter(decoded.iat)) {
          req.admin = admin;
        }
      } catch (error) {
        // Token is invalid, but we continue without authentication
        console.log('Invalid token in optional auth:', error.message);
      }
    }

    next();
  } catch (error) {
    next(error);
  }
};

module.exports = {
  protect,
  authorize,
  optionalAuth
};
