const mongoose = require('mongoose');

const crimeRecordSchema = new mongoose.Schema({
  // Reference to Criminal
  criminalId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Criminal',
    required: [true, 'Criminal ID is required']
  },
  
  // Crime Details
  type: {
    type: String,
    required: [true, 'Crime type is required'],
    enum: [
      'Theft',
      'Burglary',
      'Robbery',
      'Assault',
      'Battery',
      'Domestic Violence',
      'Drug Possession',
      'Drug Trafficking',
      'Fraud',
      'Identity Theft',
      'Cybercrime',
      'Vandalism',
      'Trespassing',
      'Weapons Violation',
      'DUI/DWI',
      'Traffic Violation',
      'Homicide',
      'Sexual Assault',
      'Kidnapping',
      'Arson',
      'Money Laundering',
      'Organized Crime',
      'Public Disorder',
      'Other'
    ]
  },
  
  subType: {
    type: String,
    trim: true,
    maxlength: [100, 'Crime subtype cannot exceed 100 characters']
  },
  
  description: {
    type: String,
    required: [true, 'Crime description is required'],
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  
  // Time and Location
  date: {
    type: Date,
    required: [true, 'Crime date is required'],
    validate: {
      validator: function(value) {
        return value <= new Date();
      },
      message: 'Crime date cannot be in the future'
    }
  },
  
  time: {
    type: String,
    validate: {
      validator: function(value) {
        if (!value) return true;
        return /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(value);
      },
      message: 'Time must be in HH:MM format'
    }
  },
  
  location: {
    address: {
      type: String,
      required: [true, 'Crime location is required'],
      trim: true,
      maxlength: [200, 'Address cannot exceed 200 characters']
    },
    city: {
      type: String,
      required: [true, 'City is required'],
      trim: true
    },
    state: {
      type: String,
      trim: true
    },
    zipCode: {
      type: String,
      trim: true
    },
    coordinates: {
      latitude: {
        type: Number,
        min: -90,
        max: 90
      },
      longitude: {
        type: Number,
        min: -180,
        max: 180
      }
    }
  },
  
  // Case Information
  caseNumber: {
    type: String,
    unique: true,
    sparse: true,
    trim: true,
    uppercase: true
  },
  
  outcome: {
    type: String,
    required: [true, 'Outcome is required'],
    enum: [
      'Under Investigation',
      'Arrested',
      'Released',
      'Charged',
      'Convicted',
      'Acquitted',
      'Case Closed',
      'Dismissed',
      'Plea Bargain',
      'Pending Trial',
      'Warrant Issued',
      'Suspect at Large'
    ]
  },
  
  severity: {
    type: String,
    enum: ['Misdemeanor', 'Felony', 'Infraction'],
    required: [true, 'Crime severity is required']
  },
  
  // Law Enforcement Details
  officer: {
    name: {
      type: String,
      required: [true, 'Officer name is required'],
      trim: true
    },
    badgeNumber: {
      type: String,
      trim: true
    },
    department: {
      type: String,
      trim: true
    }
  },
  
  arrestingOfficer: {
    name: {
      type: String,
      trim: true
    },
    badgeNumber: {
      type: String,
      trim: true
    },
    department: {
      type: String,
      trim: true
    }
  },
  
  // Additional Information
  witnesses: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    contact: {
      type: String,
      trim: true
    },
    statement: {
      type: String,
      maxlength: [500, 'Witness statement cannot exceed 500 characters']
    }
  }],
  
  evidence: [{
    type: {
      type: String,
      required: true,
      enum: ['Physical', 'Digital', 'Documentary', 'Testimonial', 'Other']
    },
    description: {
      type: String,
      required: true,
      maxlength: [300, 'Evidence description cannot exceed 300 characters']
    },
    location: {
      type: String,
      trim: true
    },
    collectedBy: {
      type: String,
      trim: true
    },
    dateCollected: {
      type: Date,
      default: Date.now
    }
  }],
  
  courtDate: {
    type: Date,
    validate: {
      validator: function(value) {
        if (!value) return true;
        return value >= new Date();
      },
      message: 'Court date must be in the future'
    }
  },
  
  sentence: {
    type: String,
    trim: true,
    maxlength: [200, 'Sentence cannot exceed 200 characters']
  },
  
  fine: {
    type: Number,
    min: 0
  },
  
  notes: {
    type: String,
    maxlength: [1000, 'Notes cannot exceed 1000 characters']
  },
  
  // System Fields
  isActive: {
    type: Boolean,
    default: true
  },
  
  createdBy: {
    type: String,
    required: true
  },
  
  lastModifiedBy: {
    type: String,
    required: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
crimeRecordSchema.index({ criminalId: 1 });
crimeRecordSchema.index({ type: 1 });
crimeRecordSchema.index({ date: -1 });
crimeRecordSchema.index({ 'location.city': 1 });
crimeRecordSchema.index({ outcome: 1 });
crimeRecordSchema.index({ severity: 1 });
crimeRecordSchema.index({ caseNumber: 1 });
crimeRecordSchema.index({ createdAt: -1 });

// Compound indexes for common queries
crimeRecordSchema.index({ criminalId: 1, date: -1 });
crimeRecordSchema.index({ type: 1, 'location.city': 1 });

// Pre-save middleware to generate case number if not provided
crimeRecordSchema.pre('save', function(next) {
  if (this.isNew && !this.caseNumber) {
    const year = new Date().getFullYear();
    const randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    this.caseNumber = `CASE-${year}-${randomNum}`;
  }
  
  if (this.isModified() && !this.isNew) {
    this.lastModifiedBy = this.lastModifiedBy || 'system';
  }
  
  next();
});

module.exports = mongoose.model('CrimeRecord', crimeRecordSchema);
