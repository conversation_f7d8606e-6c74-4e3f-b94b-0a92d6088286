import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { crimeRecordAPI } from '../utils/api';

interface CrimeRecord {
  _id: string;
  criminalId: {
    _id: string;
    fullName: string;
    alias?: string;
    nationalId?: string;
    photoUrl?: string;
  };
  type: string;
  description: string;
  date: string;
  time?: string;
  location: {
    address: string;
    city: string;
    state?: string;
    zipCode?: string;
  };
  caseNumber?: string;
  outcome: string;
  severity: string;
  officer: {
    name: string;
    badgeNumber?: string;
    department?: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface PaginatedResponse {
  success: boolean;
  count: number;
  total: number;
  pagination: {
    page: number;
    limit: number;
    pages: number;
  };
  data: CrimeRecord[];
}

const CrimeRecordList = () => {
  const [crimeRecords, setCrimeRecords] = useState<CrimeRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [filters, setFilters] = useState({
    type: 'all',
    outcome: 'all',
    severity: 'all',
    city: '',
    dateFrom: '',
    dateTo: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });
  const [sortBy, setSortBy] = useState('date');
  const [sortOrder, setSortOrder] = useState('desc');

  const crimeTypes = [
    'Theft', 'Burglary', 'Robbery', 'Assault', 'Battery', 'Domestic Violence',
    'Drug Possession', 'Drug Trafficking', 'Fraud', 'Identity Theft', 'Cybercrime',
    'Vandalism', 'Trespassing', 'Weapons Violation', 'DUI/DWI', 'Traffic Violation',
    'Homicide', 'Sexual Assault', 'Kidnapping', 'Arson', 'Money Laundering',
    'Organized Crime', 'Public Disorder', 'Other'
  ];

  const outcomes = [
    'Under Investigation', 'Arrested', 'Released', 'Charged', 'Convicted',
    'Acquitted', 'Case Closed', 'Dismissed', 'Plea Bargain', 'Pending Trial',
    'Warrant Issued', 'Suspect at Large'
  ];

  const fetchCrimeRecords = async () => {
    try {
      setIsLoading(true);
      const params = {
        page: pagination.page,
        limit: pagination.limit,
        type: filters.type !== 'all' ? filters.type : undefined,
        outcome: filters.outcome !== 'all' ? filters.outcome : undefined,
        severity: filters.severity !== 'all' ? filters.severity : undefined,
        city: filters.city || undefined,
        dateFrom: filters.dateFrom || undefined,
        dateTo: filters.dateTo || undefined,
        sortBy,
        sortOrder
      };

      const response = await crimeRecordAPI.getAll(params);
      const data: PaginatedResponse = response.data;

      setCrimeRecords(data.data);
      setPagination({
        page: data.pagination.page,
        limit: data.pagination.limit,
        total: data.total,
        pages: data.pagination.pages
      });
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to load crime records');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCrimeRecords();
  }, [pagination.page, filters, sortBy, sortOrder]);

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const getOutcomeColor = (outcome: string) => {
    switch (outcome) {
      case 'Arrested': return 'bg-red-100 text-red-800';
      case 'Convicted': return 'bg-red-100 text-red-800';
      case 'Released': return 'bg-green-100 text-green-800';
      case 'Under Investigation': return 'bg-yellow-100 text-yellow-800';
      case 'Case Closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'Felony': return 'bg-red-100 text-red-800';
      case 'Misdemeanor': return 'bg-yellow-100 text-yellow-800';
      case 'Infraction': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Crime Records</h1>
          <p className="text-gray-600">Manage and search crime records</p>
        </div>
        <div className="rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-700">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Crime Records</h1>
          <p className="text-gray-600">Manage and search crime records</p>
        </div>
        <Link
          to="/crime-records/add"
          className="btn-primary"
        >
          Add New Crime Record
        </Link>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="card-content">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Crime Type
              </label>
              <select
                className="input"
                value={filters.type}
                onChange={(e) => handleFilterChange('type', e.target.value)}
              >
                <option value="all">All Types</option>
                {crimeTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Outcome
              </label>
              <select
                className="input"
                value={filters.outcome}
                onChange={(e) => handleFilterChange('outcome', e.target.value)}
              >
                <option value="all">All Outcomes</option>
                {outcomes.map(outcome => (
                  <option key={outcome} value={outcome}>{outcome}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Severity
              </label>
              <select
                className="input"
                value={filters.severity}
                onChange={(e) => handleFilterChange('severity', e.target.value)}
              >
                <option value="all">All Severities</option>
                <option value="Felony">Felony</option>
                <option value="Misdemeanor">Misdemeanor</option>
                <option value="Infraction">Infraction</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                City
              </label>
              <input
                type="text"
                placeholder="Filter by city..."
                className="input"
                value={filters.city}
                onChange={(e) => handleFilterChange('city', e.target.value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date From
              </label>
              <input
                type="date"
                className="input"
                value={filters.dateFrom}
                onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date To
              </label>
              <input
                type="date"
                className="input"
                value={filters.dateTo}
                onChange={(e) => handleFilterChange('dateTo', e.target.value)}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Results Summary */}
      <div className="flex justify-between items-center">
        <p className="text-sm text-gray-700">
          Showing {crimeRecords.length} of {pagination.total} crime records
        </p>
        <div className="flex items-center space-x-2">
          <label className="text-sm text-gray-700">Sort by:</label>
          <select
            className="input w-auto"
            value={`${sortBy}-${sortOrder}`}
            onChange={(e) => {
              const [field, order] = e.target.value.split('-');
              setSortBy(field);
              setSortOrder(order);
            }}
          >
            <option value="date-desc">Date (Newest First)</option>
            <option value="date-asc">Date (Oldest First)</option>
            <option value="type-asc">Crime Type A-Z</option>
            <option value="type-desc">Crime Type Z-A</option>
            <option value="outcome-asc">Outcome A-Z</option>
            <option value="severity-desc">Severity (High-Low)</option>
          </select>
        </div>
      </div>

      {/* Crime Records Table */}
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      ) : crimeRecords.length === 0 ? (
        <div className="card">
          <div className="card-content">
            <div className="text-center py-8">
              <div className="text-gray-400 text-6xl mb-4">📋</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No crime records found</h3>
              <p className="text-gray-500 mb-4">
                {Object.values(filters).some(f => f && f !== 'all')
                  ? 'Try adjusting your filters to see more results.'
                  : 'Get started by adding your first crime record.'}
              </p>
              <Link to="/crime-records/add" className="btn-primary">
                Add New Crime Record
              </Link>
            </div>
          </div>
        </div>
      ) : (
        <div className="card">
          <div className="overflow-x-auto">
            <table className="table">
              <thead>
                <tr>
                  <th>Criminal</th>
                  <th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('type')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Crime Type</span>
                      {sortBy === 'type' && (
                        <span className="text-blue-600">
                          {sortOrder === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('date')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Date</span>
                      {sortBy === 'date' && (
                        <span className="text-blue-600">
                          {sortOrder === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th>Location</th>
                  <th>Case Number</th>
                  <th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('outcome')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Outcome</span>
                      {sortBy === 'outcome' && (
                        <span className="text-blue-600">
                          {sortOrder === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('severity')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Severity</span>
                      {sortBy === 'severity' && (
                        <span className="text-blue-600">
                          {sortOrder === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th>Officer</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {crimeRecords.map((record) => (
                  <tr key={record._id} className="hover:bg-gray-50">
                    <td>
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          {record.criminalId.photoUrl ? (
                            <img
                              className="h-8 w-8 rounded-full object-cover"
                              src={record.criminalId.photoUrl}
                              alt={record.criminalId.fullName}
                            />
                          ) : (
                            <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                              <span className="text-gray-600 text-xs font-medium">
                                {record.criminalId.fullName.charAt(0)}
                              </span>
                            </div>
                          )}
                        </div>
                        <div>
                          <div className="font-medium text-gray-900 text-sm">
                            {record.criminalId.fullName}
                          </div>
                          {record.criminalId.alias && (
                            <div className="text-xs text-gray-500">
                              Alias: {record.criminalId.alias}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td>
                      <span className="text-gray-900">{record.type}</span>
                    </td>
                    <td>
                      <div className="text-sm">
                        <div className="text-gray-900">{formatDate(record.date)}</div>
                        {record.time && (
                          <div className="text-gray-500">{record.time}</div>
                        )}
                      </div>
                    </td>
                    <td>
                      <div className="text-sm">
                        <div className="text-gray-900">{record.location.city}</div>
                        {record.location.state && (
                          <div className="text-gray-500">{record.location.state}</div>
                        )}
                      </div>
                    </td>
                    <td>
                      {record.caseNumber ? (
                        <span className="font-mono text-sm text-gray-900">{record.caseNumber}</span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    <td>
                      <span className={`badge ${getOutcomeColor(record.outcome)}`}>
                        {record.outcome}
                      </span>
                    </td>
                    <td>
                      <span className={`badge ${getSeverityColor(record.severity)}`}>
                        {record.severity}
                      </span>
                    </td>
                    <td>
                      <div className="text-sm">
                        <div className="text-gray-900">{record.officer.name}</div>
                        {record.officer.badgeNumber && (
                          <div className="text-gray-500 font-mono">{record.officer.badgeNumber}</div>
                        )}
                      </div>
                    </td>
                    <td>
                      <div className="flex items-center space-x-2">
                        <Link
                          to={`/crime-records/${record._id}`}
                          className="text-blue-600 hover:text-blue-900 text-sm font-medium"
                        >
                          View
                        </Link>
                        <span className="text-gray-300">|</span>
                        <Link
                          to={`/criminals/${record.criminalId._id}`}
                          className="text-green-600 hover:text-green-900 text-sm font-medium"
                        >
                          Criminal
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Page {pagination.page} of {pagination.pages}
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
              disabled={pagination.page === 1}
              className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>

            {/* Page numbers */}
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                const pageNum = i + 1;
                const isActive = pageNum === pagination.page;
                return (
                  <button
                    key={pageNum}
                    onClick={() => setPagination(prev => ({ ...prev, page: pageNum }))}
                    className={`px-3 py-1 text-sm rounded-md ${
                      isActive
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              })}
            </div>

            <button
              onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.pages, prev.page + 1) }))}
              disabled={pagination.page === pagination.pages}
              className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default CrimeRecordList;
