import { useState, useEffect } from 'react';
import { useParams, Link } from 'react-router-dom';
import { criminalAPI } from '../utils/api';

interface Criminal {
  _id: string;
  fullName: string;
  alias?: string;
  nationalId?: string;
  gender: string;
  birthDate?: string;
  age?: number;
  photoUrl?: string;
  distinctiveMarks?: string;
  fingerprintId?: string;
  knownAddresses: Array<{
    address: string;
    city: string;
    state?: string;
    zipCode?: string;
    isActive: boolean;
    dateAdded: string;
  }>;
  knownVehicles: Array<{
    make: string;
    model: string;
    year?: number;
    color?: string;
    licensePlate?: string;
    isActive: boolean;
    dateAdded: string;
  }>;
  gangAffiliation?: string;
  riskLevel: string;
  notes?: string;
  crimeCount?: number;
  createdBy: string;
  lastModifiedBy: string;
  createdAt: string;
  updatedAt: string;
}

interface CrimeRecord {
  _id: string;
  type: string;
  description: string;
  date: string;
  location: {
    address: string;
    city: string;
    state?: string;
  };
  outcome: string;
  severity: string;
  caseNumber?: string;
  officer: {
    name: string;
    badgeNumber?: string;
  };
  createdAt: string;
}

const CriminalProfile = () => {
  const { id } = useParams<{ id: string }>();
  const [criminal, setCriminal] = useState<Criminal | null>(null);
  const [crimeRecords, setCrimeRecords] = useState<CrimeRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    const fetchCriminalData = async () => {
      if (!id) return;

      try {
        setIsLoading(true);
        const response = await criminalAPI.getById(id);
        setCriminal(response.data.data.criminal);
        setCrimeRecords(response.data.data.crimeRecords || []);
      } catch (err: any) {
        setError(err.response?.data?.error || 'Failed to load criminal data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCriminalData();
  }, [id]);

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'Low': return 'bg-green-100 text-green-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getOutcomeColor = (outcome: string) => {
    switch (outcome) {
      case 'Arrested': return 'bg-red-100 text-red-800';
      case 'Convicted': return 'bg-red-100 text-red-800';
      case 'Released': return 'bg-green-100 text-green-800';
      case 'Under Investigation': return 'bg-yellow-100 text-yellow-800';
      case 'Case Closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !criminal) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Criminal Profile</h1>
          <p className="text-gray-600">View detailed criminal information</p>
        </div>
        <div className="rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-700">{error || 'Criminal not found'}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{criminal.fullName}</h1>
          <p className="text-gray-600">Criminal Profile</p>
        </div>
        <div className="flex space-x-3">
          <Link
            to={`/crime-records/add?criminalId=${criminal._id}`}
            className="btn-primary"
          >
            Add Crime Record
          </Link>
          <Link
            to="/criminals"
            className="btn-outline"
          >
            Back to List
          </Link>
        </div>
      </div>

      {/* Profile Header Card */}
      <div className="card">
        <div className="card-content">
          <div className="flex items-start space-x-6">
            <div className="flex-shrink-0">
              {criminal.photoUrl ? (
                <img
                  className="h-24 w-24 rounded-lg object-cover"
                  src={criminal.photoUrl}
                  alt={criminal.fullName}
                />
              ) : (
                <div className="h-24 w-24 rounded-lg bg-gray-300 flex items-center justify-center">
                  <span className="text-gray-600 text-2xl font-medium">
                    {criminal.fullName.charAt(0)}
                  </span>
                </div>
              )}
            </div>
            <div className="flex-1">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">{criminal.fullName}</h3>
                  {criminal.alias && (
                    <p className="text-sm text-gray-500">Alias: {criminal.alias}</p>
                  )}
                  <div className="mt-2">
                    <span className={`badge ${getRiskLevelColor(criminal.riskLevel)}`}>
                      {criminal.riskLevel} Risk
                    </span>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm">
                    <span className="text-gray-500">Gender:</span>
                    <span className="ml-2 text-gray-900">{criminal.gender}</span>
                  </div>
                  {criminal.age && (
                    <div className="text-sm">
                      <span className="text-gray-500">Age:</span>
                      <span className="ml-2 text-gray-900">{criminal.age}</span>
                    </div>
                  )}
                  {criminal.nationalId && (
                    <div className="text-sm">
                      <span className="text-gray-500">National ID:</span>
                      <span className="ml-2 text-gray-900 font-mono">{criminal.nationalId}</span>
                    </div>
                  )}
                </div>
                <div className="space-y-1">
                  <div className="text-sm">
                    <span className="text-gray-500">Crime Count:</span>
                    <span className="ml-2 text-gray-900 font-medium">{criminal.crimeCount || 0}</span>
                  </div>
                  <div className="text-sm">
                    <span className="text-gray-500">Added:</span>
                    <span className="ml-2 text-gray-900">{formatDate(criminal.createdAt)}</span>
                  </div>
                  <div className="text-sm">
                    <span className="text-gray-500">Updated:</span>
                    <span className="ml-2 text-gray-900">{formatDate(criminal.updatedAt)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', name: 'Overview' },
            { id: 'addresses', name: 'Addresses' },
            { id: 'vehicles', name: 'Vehicles' },
            { id: 'crimes', name: `Crime Records (${crimeRecords.length})` }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Personal Details */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium">Personal Details</h3>
            </div>
            <div className="card-content">
              <dl className="space-y-3">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Full Name</dt>
                  <dd className="text-sm text-gray-900">{criminal.fullName}</dd>
                </div>
                {criminal.alias && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Alias</dt>
                    <dd className="text-sm text-gray-900">{criminal.alias}</dd>
                  </div>
                )}
                {criminal.nationalId && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">National ID</dt>
                    <dd className="text-sm text-gray-900 font-mono">{criminal.nationalId}</dd>
                  </div>
                )}
                <div>
                  <dt className="text-sm font-medium text-gray-500">Gender</dt>
                  <dd className="text-sm text-gray-900">{criminal.gender}</dd>
                </div>
                {criminal.birthDate && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Birth Date</dt>
                    <dd className="text-sm text-gray-900">{formatDate(criminal.birthDate)}</dd>
                  </div>
                )}
                {criminal.age && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Age</dt>
                    <dd className="text-sm text-gray-900">{criminal.age} years old</dd>
                  </div>
                )}
                {criminal.fingerprintId && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Fingerprint ID</dt>
                    <dd className="text-sm text-gray-900 font-mono">{criminal.fingerprintId}</dd>
                  </div>
                )}
                {criminal.gangAffiliation && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Gang Affiliation</dt>
                    <dd className="text-sm text-gray-900">{criminal.gangAffiliation}</dd>
                  </div>
                )}
                <div>
                  <dt className="text-sm font-medium text-gray-500">Risk Level</dt>
                  <dd>
                    <span className={`badge ${getRiskLevelColor(criminal.riskLevel)}`}>
                      {criminal.riskLevel}
                    </span>
                  </dd>
                </div>
              </dl>
            </div>
          </div>

          {/* Additional Information */}
          <div className="space-y-6">
            {criminal.distinctiveMarks && (
              <div className="card">
                <div className="card-header">
                  <h3 className="text-lg font-medium">Distinctive Marks</h3>
                </div>
                <div className="card-content">
                  <p className="text-sm text-gray-900">{criminal.distinctiveMarks}</p>
                </div>
              </div>
            )}

            {criminal.notes && (
              <div className="card">
                <div className="card-header">
                  <h3 className="text-lg font-medium">Notes</h3>
                </div>
                <div className="card-content">
                  <p className="text-sm text-gray-900">{criminal.notes}</p>
                </div>
              </div>
            )}

            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium">Record Information</h3>
              </div>
              <div className="card-content">
                <dl className="space-y-3">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Created By</dt>
                    <dd className="text-sm text-gray-900">{criminal.createdBy}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Last Modified By</dt>
                    <dd className="text-sm text-gray-900">{criminal.lastModifiedBy}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Created</dt>
                    <dd className="text-sm text-gray-900">{formatDateTime(criminal.createdAt)}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                    <dd className="text-sm text-gray-900">{formatDateTime(criminal.updatedAt)}</dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'addresses' && (
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium">Known Addresses</h3>
          </div>
          <div className="card-content">
            {criminal.knownAddresses.length === 0 ? (
              <p className="text-center text-gray-500 py-8">No addresses recorded</p>
            ) : (
              <div className="space-y-4">
                {criminal.knownAddresses.map((address, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium text-gray-900">{address.address}</p>
                        <p className="text-sm text-gray-600">
                          {address.city}
                          {address.state && `, ${address.state}`}
                          {address.zipCode && ` ${address.zipCode}`}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          Added: {formatDate(address.dateAdded)}
                        </p>
                      </div>
                      <span className={`badge ${address.isActive ? 'badge-success' : 'bg-gray-100 text-gray-800'}`}>
                        {address.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'vehicles' && (
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium">Known Vehicles</h3>
          </div>
          <div className="card-content">
            {criminal.knownVehicles.length === 0 ? (
              <p className="text-center text-gray-500 py-8">No vehicles recorded</p>
            ) : (
              <div className="space-y-4">
                {criminal.knownVehicles.map((vehicle, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium text-gray-900">
                          {vehicle.year && `${vehicle.year} `}{vehicle.make} {vehicle.model}
                        </p>
                        <div className="text-sm text-gray-600 space-y-1">
                          {vehicle.color && <p>Color: {vehicle.color}</p>}
                          {vehicle.licensePlate && (
                            <p>License Plate: <span className="font-mono">{vehicle.licensePlate}</span></p>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          Added: {formatDate(vehicle.dateAdded)}
                        </p>
                      </div>
                      <span className={`badge ${vehicle.isActive ? 'badge-success' : 'bg-gray-100 text-gray-800'}`}>
                        {vehicle.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'crimes' && (
        <div className="card">
          <div className="card-header">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Crime Records</h3>
              <Link
                to={`/crime-records/add?criminalId=${criminal._id}`}
                className="btn-primary"
              >
                Add Crime Record
              </Link>
            </div>
          </div>
          <div className="card-content">
            {crimeRecords.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-gray-400 text-6xl mb-4">📋</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No crime records</h3>
                <p className="text-gray-500 mb-4">
                  This criminal has no recorded crimes yet.
                </p>
                <Link
                  to={`/crime-records/add?criminalId=${criminal._id}`}
                  className="btn-primary"
                >
                  Add First Crime Record
                </Link>
              </div>
            ) : (
              <div className="space-y-4">
                {crimeRecords.map((crime) => (
                  <div key={crime._id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="font-medium text-gray-900">{crime.type}</h4>
                          <span className={`badge ${getOutcomeColor(crime.outcome)}`}>
                            {crime.outcome}
                          </span>
                          {crime.caseNumber && (
                            <span className="text-xs text-gray-500 font-mono">
                              {crime.caseNumber}
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{crime.description}</p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">Date:</span>
                            <span className="ml-2 text-gray-900">{formatDate(crime.date)}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Location:</span>
                            <span className="ml-2 text-gray-900">
                              {crime.location.city}
                              {crime.location.state && `, ${crime.location.state}`}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-500">Severity:</span>
                            <span className="ml-2 text-gray-900">{crime.severity}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Officer:</span>
                            <span className="ml-2 text-gray-900">
                              {crime.officer.name}
                              {crime.officer.badgeNumber && ` (${crime.officer.badgeNumber})`}
                            </span>
                          </div>
                        </div>
                        <div className="mt-2 text-xs text-gray-500">
                          Recorded: {formatDateTime(crime.createdAt)}
                        </div>
                      </div>
                      <div className="ml-4">
                        <Link
                          to={`/crime-records/${crime._id}`}
                          className="text-blue-600 hover:text-blue-900 text-sm font-medium"
                        >
                          View Details
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CriminalProfile;
